<?php
/**
 * Test Dashboard - Bypass authentication for testing
 */

// Enable error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Start session
session_start();

// Set fake session for testing
$_SESSION['admin_id'] = 1;
$_SESSION['admin_username'] = 'test_admin';

echo "<h1>Testing Dashboard Components</h1>";

try {
    require_once 'includes/config.php';
    echo "✓ Config loaded successfully<br>";
} catch (Exception $e) {
    die("✗ Configuration error: " . $e->getMessage());
}

// Check database connection
if (!isset($conn) || !$conn) {
    die("✗ Database connection failed. Please check your configuration.");
}
echo "✓ Database connected<br>";

try {
    require_once 'includes/functions.php';
    echo "✓ Functions loaded successfully<br>";
} catch (Exception $e) {
    die("✗ Functions file error: " . $e->getMessage());
}

// Test getDashboardStats function
try {
    $stats = getDashboardStats($conn);
    echo "✓ Dashboard stats loaded successfully<br>";
    echo "<h2>Dashboard Statistics:</h2>";
    echo "<ul>";
    echo "<li>Total Servers: " . $stats['total_servers'] . "</li>";
    echo "<li>Active Servers: " . $stats['active_servers'] . "</li>";
    echo "<li>Total Ads: " . $stats['total_ads'] . "</li>";
    echo "<li>Running Ads: " . $stats['running_ads'] . "</li>";
    echo "<li>Total Notifications: " . $stats['total_notifications'] . "</li>";
    echo "<li>Pending Notifications: " . $stats['pending_notifications'] . "</li>";
    echo "<li>Total Contacts: " . $stats['total_contacts'] . "</li>";
    echo "<li>Unread Contacts: " . $stats['unread_contacts'] . "</li>";
    echo "<li>Recent Activities: " . count($stats['recent_activities']) . "</li>";
    echo "<li>Servers List: " . count($stats['servers']) . "</li>";
    echo "</ul>";
} catch (Exception $e) {
    echo "✗ Dashboard stats failed: " . $e->getMessage() . "<br>";
    echo "Error details: " . $e->getTraceAsString() . "<br>";
}

echo "<br><h2>Testing Header Include</h2>";
try {
    ob_start();
    include 'includes/header.php';
    $header_content = ob_get_clean();
    echo "✓ Header included successfully (Length: " . strlen($header_content) . " chars)<br>";
    
    // Show first 200 characters of header
    echo "<h3>Header Preview:</h3>";
    echo "<pre>" . htmlspecialchars(substr($header_content, 0, 200)) . "...</pre>";
} catch (Exception $e) {
    echo "✗ Header include failed: " . $e->getMessage() . "<br>";
}

echo "<br><h2>All Tests Complete</h2>";
echo "<p>If you see this message, the core components are working.</p>";
echo "<p><a href='index.php'>Try the actual index.php now</a></p>";
?>
