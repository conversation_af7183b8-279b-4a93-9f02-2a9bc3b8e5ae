<?php
/**
 * Debug script to diagnose 500 Internal Server Error
 */

// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);
ini_set('log_errors', 1);

echo "<h1>5G Smart VPN Admin Panel - Debug Information</h1>";

// Check PHP version
echo "<h2>PHP Information</h2>";
echo "PHP Version: " . phpversion() . "<br>";
echo "Server Software: " . $_SERVER['SERVER_SOFTWARE'] . "<br>";
echo "Document Root: " . $_SERVER['DOCUMENT_ROOT'] . "<br>";
echo "Script Path: " . __FILE__ . "<br>";

// Test database connection
echo "<h2>Database Connection Test</h2>";
try {
    // Include config
    $config_path = dirname(__DIR__) . '/config.php';
    echo "Config path: " . $config_path . "<br>";
    
    if (file_exists($config_path)) {
        echo "Config file exists: YES<br>";
        require_once $config_path;
        
        if (isset($conn) && $conn) {
            echo "Database connection: SUCCESS<br>";
            
            // Test basic query
            $result = mysqli_query($conn, "SELECT 1 as test");
            if ($result) {
                echo "Database query test: SUCCESS<br>";
                
                // Check if required tables exist
                $tables = ['servers', 'custom_ads', 'notifications', 'contact'];
                foreach ($tables as $table) {
                    $check = mysqli_query($conn, "SHOW TABLES LIKE '$table'");
                    $exists = mysqli_num_rows($check) > 0 ? "EXISTS" : "MISSING";
                    echo "Table '$table': $exists<br>";
                }
            } else {
                echo "Database query test: FAILED - " . mysqli_error($conn) . "<br>";
            }
        } else {
            echo "Database connection: FAILED<br>";
            if (isset($conn)) {
                echo "Error: " . mysqli_connect_error() . "<br>";
            }
        }
    } else {
        echo "Config file exists: NO<br>";
    }
} catch (Exception $e) {
    echo "Exception: " . $e->getMessage() . "<br>";
}

// Test session functionality
echo "<h2>Session Test</h2>";
session_start();
echo "Session started: " . (session_status() === PHP_SESSION_ACTIVE ? "YES" : "NO") . "<br>";
echo "Session ID: " . session_id() . "<br>";

// Test file permissions
echo "<h2>File Permissions</h2>";
$files_to_check = [
    __DIR__ . '/includes/config.php',
    __DIR__ . '/includes/functions.php',
    __DIR__ . '/index.php',
    dirname(__DIR__) . '/config.php'
];

foreach ($files_to_check as $file) {
    if (file_exists($file)) {
        $perms = substr(sprintf('%o', fileperms($file)), -4);
        echo basename($file) . ": EXISTS (permissions: $perms)<br>";
    } else {
        echo basename($file) . ": MISSING<br>";
    }
}

// Test functions.php
echo "<h2>Functions Test</h2>";
try {
    require_once __DIR__ . '/includes/functions.php';
    echo "functions.php loaded: SUCCESS<br>";
    
    if (function_exists('getDashboardStats')) {
        echo "getDashboardStats function: EXISTS<br>";
        
        if (isset($conn) && $conn) {
            try {
                $stats = getDashboardStats($conn);
                echo "getDashboardStats execution: SUCCESS<br>";
                echo "Stats returned: " . count($stats) . " items<br>";
            } catch (Exception $e) {
                echo "getDashboardStats execution: FAILED - " . $e->getMessage() . "<br>";
            }
        }
    } else {
        echo "getDashboardStats function: MISSING<br>";
    }
} catch (Exception $e) {
    echo "functions.php load: FAILED - " . $e->getMessage() . "<br>";
}

// Check memory and execution limits
echo "<h2>PHP Limits</h2>";
echo "Memory Limit: " . ini_get('memory_limit') . "<br>";
echo "Max Execution Time: " . ini_get('max_execution_time') . "<br>";
echo "Upload Max Filesize: " . ini_get('upload_max_filesize') . "<br>";
echo "Post Max Size: " . ini_get('post_max_size') . "<br>";

// Check for common extensions
echo "<h2>PHP Extensions</h2>";
$required_extensions = ['mysqli', 'json', 'session', 'mbstring'];
foreach ($required_extensions as $ext) {
    $loaded = extension_loaded($ext) ? "LOADED" : "MISSING";
    echo "$ext: $loaded<br>";
}

echo "<h2>Debug Complete</h2>";
echo "If you see this message, PHP is working correctly.<br>";
echo "Check the information above for any issues.<br>";
?>
