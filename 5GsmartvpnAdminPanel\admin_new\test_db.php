<?php
/**
 * Simple database test script
 */

// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>Database Test</h1>";

// Database Configuration
$host = "localhost";
$user = "root";
$pass = "";
$db = "5gsmartvpnnewupdate";

echo "Attempting to connect to database...<br>";

// Create database connection
$conn = mysqli_connect($host, $user, $pass, $db);

// Check connection
if (!$conn) {
    echo "Connection failed: " . mysqli_connect_error() . "<br>";
    
    // Try to connect without database to check if MySQL is running
    $conn_test = mysqli_connect($host, $user, $pass);
    if (!$conn_test) {
        echo "MySQL server is not running or credentials are wrong.<br>";
    } else {
        echo "MySQL server is running, but database '$db' doesn't exist.<br>";
        echo "Available databases:<br>";
        $result = mysqli_query($conn_test, "SHOW DATABASES");
        while ($row = mysqli_fetch_array($result)) {
            echo "- " . $row[0] . "<br>";
        }
        mysqli_close($conn_test);
    }
    exit();
}

echo "Database connection successful!<br>";

// Set charset
mysqli_set_charset($conn, 'utf8mb4');

// Check required tables
$required_tables = ['servers', 'custom_ads', 'notifications', 'contact'];

echo "<h2>Checking Required Tables</h2>";
foreach ($required_tables as $table) {
    $result = mysqli_query($conn, "SHOW TABLES LIKE '$table'");
    if (mysqli_num_rows($result) > 0) {
        echo "✓ Table '$table' exists<br>";
        
        // Get row count
        $count_result = mysqli_query($conn, "SELECT COUNT(*) as count FROM $table");
        $count = mysqli_fetch_assoc($count_result)['count'];
        echo "&nbsp;&nbsp;Rows: $count<br>";
    } else {
        echo "✗ Table '$table' is missing<br>";
    }
}

// Test a simple query
echo "<h2>Testing Basic Query</h2>";
$test_query = "SELECT 1 as test_value";
$result = mysqli_query($conn, $test_query);
if ($result) {
    $row = mysqli_fetch_assoc($result);
    echo "✓ Basic query successful: " . $row['test_value'] . "<br>";
} else {
    echo "✗ Basic query failed: " . mysqli_error($conn) . "<br>";
}

mysqli_close($conn);

echo "<h2>Test Complete</h2>";
?>
