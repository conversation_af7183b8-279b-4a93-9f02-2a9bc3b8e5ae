<?php
/**
 * Final Verification Script
 * Tests all components after fixes
 */

session_start();

echo "<h1>🔍 Final System Verification</h1>";
echo "<style>
    body { font-family: Arial, sans-serif; margin: 20px; }
    .success { color: #28a745; }
    .error { color: #dc3545; }
    .warning { color: #ffc107; }
    .info { color: #17a2b8; }
    .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
    .test-result { padding: 10px; margin: 5px 0; border-radius: 3px; }
    .pass { background-color: #d4edda; border: 1px solid #c3e6cb; }
    .fail { background-color: #f8d7da; border: 1px solid #f5c6cb; }
</style>";

$tests_passed = 0;
$total_tests = 0;

// Test 1: Configuration Loading
echo "<div class='test-section'>";
echo "<h2>📁 Configuration Test</h2>";
$total_tests++;
try {
    require_once 'includes/config.php';
    if (defined('ADMIN_CONFIG_LOADED') && isset($conn)) {
        echo "<div class='test-result pass'>✅ Configuration loaded successfully</div>";
        $tests_passed++;
    } else {
        echo "<div class='test-result fail'>❌ Configuration failed to load</div>";
    }
} catch (Exception $e) {
    echo "<div class='test-result fail'>❌ Configuration error: " . $e->getMessage() . "</div>";
}
echo "</div>";

// Test 2: Database Connection
echo "<div class='test-section'>";
echo "<h2>🗄️ Database Connection Test</h2>";
$total_tests++;
if (isset($conn) && $conn instanceof mysqli) {
    if ($conn->ping()) {
        echo "<div class='test-result pass'>✅ Database connection active</div>";
        $tests_passed++;
    } else {
        echo "<div class='test-result fail'>❌ Database connection inactive</div>";
    }
} else {
    echo "<div class='test-result fail'>❌ Database connection not established</div>";
}
echo "</div>";

// Test 3: Required Tables
echo "<div class='test-section'>";
echo "<h2>📊 Database Tables Test</h2>";
$required_tables = ['servers', 'custom_ads', 'notifications', 'contact', 'admin_users', 'settings'];
$tables_found = 0;

foreach ($required_tables as $table) {
    $total_tests++;
    $result = mysqli_query($conn, "SHOW TABLES LIKE '$table'");
    if ($result && mysqli_num_rows($result) > 0) {
        echo "<div class='test-result pass'>✅ Table '$table' exists</div>";
        $tests_passed++;
        $tables_found++;
    } else {
        echo "<div class='test-result fail'>❌ Table '$table' missing</div>";
    }
}
echo "</div>";

// Test 4: Functions Loading
echo "<div class='test-section'>";
echo "<h2>⚙️ Functions Test</h2>";
$total_tests++;
try {
    require_once 'includes/functions.php';
    if (function_exists('getDashboardStats')) {
        echo "<div class='test-result pass'>✅ Functions loaded successfully</div>";
        $tests_passed++;
    } else {
        echo "<div class='test-result fail'>❌ getDashboardStats function not found</div>";
    }
} catch (Exception $e) {
    echo "<div class='test-result fail'>❌ Functions error: " . $e->getMessage() . "</div>";
}
echo "</div>";

// Test 5: Dashboard Stats
echo "<div class='test-section'>";
echo "<h2>📈 Dashboard Stats Test</h2>";
$total_tests++;
try {
    $stats = getDashboardStats($conn);
    if (is_array($stats) && isset($stats['total_servers'])) {
        echo "<div class='test-result pass'>✅ Dashboard stats working</div>";
        echo "<div class='info'>Servers: {$stats['total_servers']}, Ads: {$stats['total_ads']}, Notifications: {$stats['total_notifications']}</div>";
        $tests_passed++;
    } else {
        echo "<div class='test-result fail'>❌ Dashboard stats invalid</div>";
    }
} catch (Exception $e) {
    echo "<div class='test-result fail'>❌ Dashboard stats error: " . $e->getMessage() . "</div>";
}
echo "</div>";

// Test 6: Admin User
echo "<div class='test-section'>";
echo "<h2>👤 Admin User Test</h2>";
$total_tests++;
$result = mysqli_query($conn, "SELECT COUNT(*) as count FROM admin_users WHERE username = 'admin'");
if ($result) {
    $row = mysqli_fetch_assoc($result);
    if ($row['count'] > 0) {
        echo "<div class='test-result pass'>✅ Admin user exists</div>";
        $tests_passed++;
    } else {
        echo "<div class='test-result fail'>❌ Admin user not found</div>";
    }
} else {
    echo "<div class='test-result fail'>❌ Cannot check admin user</div>";
}
echo "</div>";

// Test 7: Settings
echo "<div class='test-section'>";
echo "<h2>⚙️ Settings Test</h2>";
$total_tests++;
$result = mysqli_query($conn, "SELECT COUNT(*) as count FROM settings");
if ($result) {
    $row = mysqli_fetch_assoc($result);
    if ($row['count'] > 0) {
        echo "<div class='test-result pass'>✅ Settings initialized ({$row['count']} settings)</div>";
        $tests_passed++;
    } else {
        echo "<div class='test-result fail'>❌ No settings found</div>";
    }
} else {
    echo "<div class='test-result fail'>❌ Cannot check settings</div>";
}
echo "</div>";

// Test 8: File Permissions
echo "<div class='test-section'>";
echo "<h2>📂 File Permissions Test</h2>";
$critical_files = [
    'index.php',
    'login.php',
    'includes/header.php',
    'assets/css/admin.css'
];

foreach ($critical_files as $file) {
    $total_tests++;
    if (file_exists($file) && is_readable($file)) {
        echo "<div class='test-result pass'>✅ $file is accessible</div>";
        $tests_passed++;
    } else {
        echo "<div class='test-result fail'>❌ $file is not accessible</div>";
    }
}
echo "</div>";

// Final Results
echo "<div class='test-section'>";
echo "<h2>📊 Final Results</h2>";
$success_rate = round(($tests_passed / $total_tests) * 100, 1);

if ($success_rate >= 90) {
    echo "<div class='test-result pass'>";
    echo "<h3>🎉 System Status: EXCELLENT</h3>";
    echo "<p>Passed: $tests_passed/$total_tests tests ($success_rate%)</p>";
    echo "<p>Your admin panel should be working perfectly!</p>";
} elseif ($success_rate >= 70) {
    echo "<div class='test-result warning'>";
    echo "<h3>⚠️ System Status: GOOD</h3>";
    echo "<p>Passed: $tests_passed/$total_tests tests ($success_rate%)</p>";
    echo "<p>Most features should work, but some issues remain.</p>";
} else {
    echo "<div class='test-result fail'>";
    echo "<h3>❌ System Status: NEEDS ATTENTION</h3>";
    echo "<p>Passed: $tests_passed/$total_tests tests ($success_rate%)</p>";
    echo "<p>Several critical issues need to be resolved.</p>";
}
echo "</div>";
echo "</div>";

echo "<div class='test-section'>";
echo "<h2>🔗 Quick Links</h2>";
echo "<ul>";
echo "<li><a href='login.php'>🔐 Admin Login</a> (username: admin, password: 1234)</li>";
echo "<li><a href='index.php'>📊 Dashboard</a></li>";
echo "<li><a href='servers.php'>🌐 Servers Management</a></li>";
echo "<li><a href='ads.php'>📱 Ads Management</a></li>";
echo "<li><a href='api/status.php'>🔌 API Status</a></li>";
echo "<li><a href='api/config.php?pkg=test&timestamp=" . time() . "&signature=" . hash_hmac('sha256', time(), 'your-api-key') . "'>📡 API Config</a></li>";
echo "</ul>";
echo "</div>";
?>
