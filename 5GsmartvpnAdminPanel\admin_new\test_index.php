<?php
/**
 * Test version of index.php without authentication
 */

error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>Testing Admin Panel Components</h1>";

// Test 1: Basic PHP functionality
echo "<h2>1. PHP Test</h2>";
echo "PHP Version: " . phpversion() . "<br>";
echo "✓ PHP is working<br><br>";

// Test 2: Database connection
echo "<h2>2. Database Connection Test</h2>";
try {
    require_once dirname(__DIR__) . '/config.php';
    
    if (isset($conn) && $conn) {
        echo "✓ Database connected successfully<br>";
        
        // Test query
        $result = mysqli_query($conn, "SELECT 1");
        if ($result) {
            echo "✓ Database queries working<br>";
        } else {
            echo "✗ Database query failed: " . mysqli_error($conn) . "<br>";
        }
    } else {
        echo "✗ Database connection failed<br>";
    }
} catch (Exception $e) {
    echo "✗ Database error: " . $e->getMessage() . "<br>";
}

echo "<br>";

// Test 3: Functions file
echo "<h2>3. Functions Test</h2>";
try {
    require_once 'includes/functions.php';
    echo "✓ Functions file loaded<br>";
    
    if (function_exists('getDashboardStats')) {
        echo "✓ getDashboardStats function exists<br>";
        
        // Test the function
        if (isset($conn) && $conn) {
            try {
                $stats = getDashboardStats($conn);
                echo "✓ getDashboardStats executed successfully<br>";
                echo "Stats keys: " . implode(', ', array_keys($stats)) . "<br>";
            } catch (Exception $e) {
                echo "✗ getDashboardStats failed: " . $e->getMessage() . "<br>";
            }
        }
    } else {
        echo "✗ getDashboardStats function not found<br>";
    }
} catch (Exception $e) {
    echo "✗ Functions error: " . $e->getMessage() . "<br>";
}

echo "<br>";

// Test 4: Session functionality
echo "<h2>4. Session Test</h2>";
session_start();
echo "✓ Session started<br>";
echo "Session ID: " . session_id() . "<br>";

// Set test session variables
$_SESSION['admin_id'] = 1;
$_SESSION['admin_username'] = 'test_admin';
echo "✓ Session variables set<br><br>";

// Test 5: Include header (if it exists)
echo "<h2>5. Header Include Test</h2>";
if (file_exists('includes/header.php')) {
    echo "✓ Header file exists<br>";
    try {
        ob_start();
        include 'includes/header.php';
        $header_content = ob_get_clean();
        echo "✓ Header included successfully<br>";
        echo "Header length: " . strlen($header_content) . " characters<br>";
    } catch (Exception $e) {
        echo "✗ Header include failed: " . $e->getMessage() . "<br>";
    }
} else {
    echo "✗ Header file not found<br>";
}

echo "<br>";

// Test 6: Try to load the actual dashboard stats
echo "<h2>6. Dashboard Stats Test</h2>";
if (isset($conn) && $conn && function_exists('getDashboardStats')) {
    try {
        $stats = getDashboardStats($conn);
        echo "✓ Dashboard stats loaded successfully<br>";
        echo "<pre>";
        print_r($stats);
        echo "</pre>";
    } catch (Exception $e) {
        echo "✗ Dashboard stats failed: " . $e->getMessage() . "<br>";
        echo "Error details: " . $e->getTraceAsString() . "<br>";
    }
}

echo "<h2>Test Complete</h2>";
echo "If you can see this, the basic components are working.<br>";
echo "Now try the actual index.php file.";
?>
