<?php
/**
 * Comprehensive Diagnostic Tool for 5G Smart VPN Admin Panel
 * Identifies all configuration conflicts and 500 error causes
 */

// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);
ini_set('log_errors', 1);

echo "<h1>🔍 Comprehensive 500 Error Diagnostic</h1>";
echo "<style>
    body { font-family: Arial, sans-serif; margin: 20px; }
    .success { color: #28a745; }
    .error { color: #dc3545; }
    .warning { color: #ffc107; }
    .info { color: #17a2b8; }
    .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
    pre { background: #f8f9fa; padding: 10px; border-radius: 3px; overflow-x: auto; }
</style>";

// 1. Configuration Files Analysis
echo "<div class='section'>";
echo "<h2>📁 Configuration Files Analysis</h2>";

$config_files = [
    'Main Config' => dirname(__DIR__) . '/config.php',
    'Admin Config' => __DIR__ . '/includes/config.php', 
    'DB Config' => __DIR__ . '/db.php',
    'API Config' => __DIR__ . '/api/config.php'
];

foreach ($config_files as $name => $path) {
    if (file_exists($path)) {
        echo "<p class='success'>✓ $name: EXISTS ($path)</p>";
        
        // Check file permissions
        $perms = substr(sprintf('%o', fileperms($path)), -4);
        echo "<p class='info'>&nbsp;&nbsp;Permissions: $perms</p>";
        
        // Check file size
        $size = filesize($path);
        echo "<p class='info'>&nbsp;&nbsp;Size: $size bytes</p>";
        
        // Try to include and check for syntax errors
        ob_start();
        $error_level = error_reporting(0);
        $include_result = @include_once($path);
        error_reporting($error_level);
        $output = ob_get_clean();
        
        if ($include_result !== false) {
            echo "<p class='success'>&nbsp;&nbsp;Syntax: VALID</p>";
        } else {
            echo "<p class='error'>&nbsp;&nbsp;Syntax: ERROR - Cannot include file</p>";
        }
    } else {
        echo "<p class='error'>✗ $name: MISSING ($path)</p>";
    }
}
echo "</div>";

// 2. Database Connection Tests
echo "<div class='section'>";
echo "<h2>🗄️ Database Connection Analysis</h2>";

// Test main config connection
echo "<h3>Main Config Database Test:</h3>";
try {
    require_once dirname(__DIR__) . '/config.php';
    if (isset($conn) && $conn instanceof mysqli) {
        if ($conn->ping()) {
            echo "<p class='success'>✓ Main config MySQLi connection: ACTIVE</p>";
            echo "<p class='info'>&nbsp;&nbsp;Host: " . $conn->host_info . "</p>";
            echo "<p class='info'>&nbsp;&nbsp;Server: " . $conn->server_info . "</p>";
        } else {
            echo "<p class='error'>✗ Main config MySQLi connection: INACTIVE</p>";
        }
    } else {
        echo "<p class='error'>✗ Main config MySQLi connection: NOT ESTABLISHED</p>";
    }
} catch (Exception $e) {
    echo "<p class='error'>✗ Main config error: " . $e->getMessage() . "</p>";
}

// Test db.php connection
echo "<h3>DB.php Database Test:</h3>";
try {
    require_once __DIR__ . '/db.php';
    
    // Test PDO connection
    $db = getDB();
    $test = $db->fetchOne("SELECT 1 as test, NOW() as current_time");
    if ($test) {
        echo "<p class='success'>✓ DB.php PDO connection: ACTIVE</p>";
        echo "<p class='info'>&nbsp;&nbsp;Test query result: " . $test['test'] . "</p>";
        echo "<p class='info'>&nbsp;&nbsp;Server time: " . $test['current_time'] . "</p>";
    }
    
    // Test legacy MySQLi from db.php
    if (isset($conn) && $conn instanceof mysqli) {
        if ($conn->ping()) {
            echo "<p class='success'>✓ DB.php MySQLi connection: ACTIVE</p>";
        } else {
            echo "<p class='error'>✗ DB.php MySQLi connection: INACTIVE</p>";
        }
    }
} catch (Exception $e) {
    echo "<p class='error'>✗ DB.php error: " . $e->getMessage() . "</p>";
}
echo "</div>";

// 3. Required Tables Check
echo "<div class='section'>";
echo "<h2>📊 Database Tables Analysis</h2>";

$required_tables = [
    'servers' => 'VPN server configurations',
    'custom_ads' => 'Custom advertisement data',
    'notifications' => 'Push notification records',
    'contact' => 'Contact form submissions',
    'admin_users' => 'Admin user accounts',
    'admin_logs' => 'Admin activity logs',
    'settings' => 'Application settings'
];

try {
    if (isset($conn) && $conn instanceof mysqli) {
        foreach ($required_tables as $table => $description) {
            $result = $conn->query("SHOW TABLES LIKE '$table'");
            if ($result && $result->num_rows > 0) {
                echo "<p class='success'>✓ Table '$table': EXISTS ($description)</p>";
                
                // Get row count
                $count_result = $conn->query("SELECT COUNT(*) as count FROM `$table`");
                if ($count_result) {
                    $count = $count_result->fetch_assoc()['count'];
                    echo "<p class='info'>&nbsp;&nbsp;Records: $count</p>";
                }
                
                // Check table structure for critical tables
                if (in_array($table, ['servers', 'custom_ads'])) {
                    $structure = $conn->query("DESCRIBE `$table`");
                    if ($structure) {
                        echo "<p class='info'>&nbsp;&nbsp;Columns: ";
                        $columns = [];
                        while ($row = $structure->fetch_assoc()) {
                            $columns[] = $row['Field'];
                        }
                        echo implode(', ', $columns) . "</p>";
                    }
                }
            } else {
                echo "<p class='error'>✗ Table '$table': MISSING ($description)</p>";
            }
        }
    } else {
        echo "<p class='error'>✗ Cannot check tables - no database connection</p>";
    }
} catch (Exception $e) {
    echo "<p class='error'>✗ Table check error: " . $e->getMessage() . "</p>";
}
echo "</div>";

// 4. PHP Environment Check
echo "<div class='section'>";
echo "<h2>🐘 PHP Environment Analysis</h2>";

echo "<p class='info'>PHP Version: " . phpversion() . "</p>";
echo "<p class='info'>Server Software: " . ($_SERVER['SERVER_SOFTWARE'] ?? 'Unknown') . "</p>";
echo "<p class='info'>Document Root: " . $_SERVER['DOCUMENT_ROOT'] . "</p>";
echo "<p class='info'>Current Script: " . __FILE__ . "</p>";

$required_extensions = ['mysqli', 'pdo', 'pdo_mysql', 'json', 'session', 'mbstring', 'openssl'];
echo "<h3>Required Extensions:</h3>";
foreach ($required_extensions as $ext) {
    $loaded = extension_loaded($ext);
    $status = $loaded ? 'success' : 'error';
    $symbol = $loaded ? '✓' : '✗';
    echo "<p class='$status'>$symbol $ext: " . ($loaded ? 'LOADED' : 'MISSING') . "</p>";
}

echo "<h3>PHP Configuration:</h3>";
$php_settings = [
    'memory_limit' => ini_get('memory_limit'),
    'max_execution_time' => ini_get('max_execution_time'),
    'upload_max_filesize' => ini_get('upload_max_filesize'),
    'post_max_size' => ini_get('post_max_size'),
    'display_errors' => ini_get('display_errors') ? 'On' : 'Off',
    'log_errors' => ini_get('log_errors') ? 'On' : 'Off'
];

foreach ($php_settings as $setting => $value) {
    echo "<p class='info'>$setting: $value</p>";
}
echo "</div>";

// 5. File System Permissions
echo "<div class='section'>";
echo "<h2>📂 File System Permissions</h2>";

$critical_files = [
    __DIR__ . '/index.php',
    __DIR__ . '/login.php',
    __DIR__ . '/includes/functions.php',
    __DIR__ . '/includes/header.php',
    __DIR__ . '/includes/sidebar.php',
    __DIR__ . '/assets/css/admin.css'
];

foreach ($critical_files as $file) {
    if (file_exists($file)) {
        $perms = substr(sprintf('%o', fileperms($file)), -4);
        $readable = is_readable($file) ? 'YES' : 'NO';
        echo "<p class='success'>✓ " . basename($file) . ": EXISTS (Perms: $perms, Readable: $readable)</p>";
    } else {
        echo "<p class='error'>✗ " . basename($file) . ": MISSING</p>";
    }
}
echo "</div>";

// 6. Session Analysis
echo "<div class='section'>";
echo "<h2>🔐 Session Analysis</h2>";

session_start();
echo "<p class='info'>Session Status: " . (session_status() === PHP_SESSION_ACTIVE ? 'ACTIVE' : 'INACTIVE') . "</p>";
echo "<p class='info'>Session ID: " . session_id() . "</p>";
echo "<p class='info'>Session Save Path: " . session_save_path() . "</p>";

if (isset($_SESSION['admin_id'])) {
    echo "<p class='success'>✓ Admin session exists (ID: " . $_SESSION['admin_id'] . ")</p>";
    echo "<p class='info'>&nbsp;&nbsp;Username: " . ($_SESSION['admin_username'] ?? 'Not set') . "</p>";
} else {
    echo "<p class='warning'>⚠ No admin session found</p>";
}
echo "</div>";

echo "<div class='section'>";
echo "<h2>🎯 Recommendations</h2>";
echo "<ol>";
echo "<li><strong>Configuration Conflict:</strong> You have multiple config files. Use db.php as primary.</li>";
echo "<li><strong>Database Tables:</strong> Run fix_database.php to create missing tables.</li>";
echo "<li><strong>Error Logging:</strong> Check Apache error logs for specific 500 error details.</li>";
echo "<li><strong>File Permissions:</strong> Ensure all PHP files have proper read permissions.</li>";
echo "<li><strong>Session Issues:</strong> Clear browser cache and try fresh login.</li>";
echo "</ol>";
echo "</div>";

echo "<p><strong>Next Steps:</strong></p>";
echo "<ul>";
echo "<li><a href='fix_database.php'>Run Database Fix Script</a></li>";
echo "<li><a href='test_simple.php'>Test Simple PHP Functionality</a></li>";
echo "<li><a href='login.php'>Try Admin Login</a></li>";
echo "</ul>";
?>
