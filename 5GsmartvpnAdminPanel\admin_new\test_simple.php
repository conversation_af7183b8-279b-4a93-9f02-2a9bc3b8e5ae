<?php
// Simple test to check basic functionality
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "PHP is working!<br>";
echo "Current directory: " . __DIR__ . "<br>";

// Test database connection
$host = "localhost";
$user = "root";
$pass = "";
$db = "5gsmartvpnnewupdate";

$conn = mysqli_connect($host, $user, $pass, $db);

if ($conn) {
    echo "Database connection: SUCCESS<br>";
    
    // Test a simple query
    $result = mysqli_query($conn, "SELECT 1");
    if ($result) {
        echo "Database query: SUCCESS<br>";
    } else {
        echo "Database query: FAILED - " . mysqli_error($conn) . "<br>";
    }
} else {
    echo "Database connection: FAILED - " . mysqli_connect_error() . "<br>";
}

echo "Test complete!";
?>
