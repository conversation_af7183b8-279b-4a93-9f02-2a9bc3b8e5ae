<?php
/**
 * Database Fix Script
 * This script will check and create missing database tables
 */

error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>Database Fix Script</h1>";

// Database connection
$host = "localhost";
$user = "root";
$pass = "";
$db = "5gsmartvpnnewupdate";

$conn = mysqli_connect($host, $user, $pass, $db);

if (!$conn) {
    die("Connection failed: " . mysqli_connect_error());
}

echo "Connected to database successfully!<br><br>";

// Check and create tables
$tables_to_check = [
    'servers' => "
        CREATE TABLE IF NOT EXISTS `servers` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `name` varchar(255) NOT NULL,
            `flagURL` varchar(255) DEFAULT NULL,
            `type` varchar(50) DEFAULT 'free',
            `status` tinyint(1) DEFAULT 1,
            `pos` int(11) DEFAULT 0,
            `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
    ",
    
    'custom_ads' => "
        CREATE TABLE IF NOT EXISTS `custom_ads` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `title` varchar(255) NOT NULL,
            `description` text,
            `image_url` varchar(255) DEFAULT NULL,
            `click_url` varchar(255) DEFAULT NULL,
            `date_start` date DEFAULT NULL,
            `date_end` date DEFAULT NULL,
            `on` tinyint(1) DEFAULT 1,
            `view_count` int(11) DEFAULT 0,
            `click_count` int(11) DEFAULT 0,
            `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
    ",
    
    'notifications' => "
        CREATE TABLE IF NOT EXISTS `notifications` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `title` varchar(255) NOT NULL,
            `message` text NOT NULL,
            `status` enum('pending','sent','failed','scheduled') DEFAULT 'pending',
            `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
    ",
    
    'contact' => "
        CREATE TABLE IF NOT EXISTS `contact` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `name` varchar(255) NOT NULL,
            `email` varchar(255) NOT NULL,
            `subject` varchar(255) DEFAULT NULL,
            `message` text NOT NULL,
            `unread` tinyint(1) DEFAULT 1,
            `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
    ",
    
    'admin_logs' => "
        CREATE TABLE IF NOT EXISTS `admin_logs` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `admin_id` int(11) DEFAULT 1,
            `admin_username` varchar(255) DEFAULT 'admin',
            `action` varchar(100) DEFAULT NULL,
            `description` text,
            `ip_address` varchar(45) DEFAULT '127.0.0.1',
            `user_agent` text,
            `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`),
            KEY `idx_created_at` (`created_at`),
            KEY `idx_action` (`action`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
    "
];

foreach ($tables_to_check as $table_name => $create_sql) {
    // Check if table exists
    $result = mysqli_query($conn, "SHOW TABLES LIKE '$table_name'");
    
    if (mysqli_num_rows($result) == 0) {
        echo "Table '$table_name' does not exist. Creating...<br>";
        
        if (mysqli_query($conn, $create_sql)) {
            echo "✓ Table '$table_name' created successfully!<br>";
        } else {
            echo "✗ Error creating table '$table_name': " . mysqli_error($conn) . "<br>";
        }
    } else {
        echo "✓ Table '$table_name' already exists.<br>";
    }
}

// Insert sample data if tables are empty
echo "<br><h2>Checking for sample data...</h2>";

// Check servers table
$result = mysqli_query($conn, "SELECT COUNT(*) as count FROM servers");
if ($result) {
    $row = mysqli_fetch_assoc($result);
    if ($row['count'] == 0) {
        echo "Adding sample servers...<br>";
        $sample_servers = [
            "INSERT INTO servers (name, flagURL, type, status, pos) VALUES ('USA Server', 'flag/united-states-of-america.png', 'free', 1, 1)",
            "INSERT INTO servers (name, flagURL, type, status, pos) VALUES ('UK Server', 'flag/united-kingdom.png', 'premium', 1, 2)",
            "INSERT INTO servers (name, flagURL, type, status, pos) VALUES ('Germany Server', 'flag/germany.png', 'free', 1, 3)",
            "INSERT INTO servers (name, flagURL, type, status, pos) VALUES ('Canada Server', 'flag/canada.png', 'free', 1, 4)",
            "INSERT INTO servers (name, flagURL, type, status, pos) VALUES ('Japan Server', 'flag/japan.png', 'premium', 1, 5)"
        ];

        foreach ($sample_servers as $sql) {
            if (mysqli_query($conn, $sql)) {
                echo "✓ Server added<br>";
            } else {
                echo "✗ Error adding server: " . mysqli_error($conn) . "<br>";
            }
        }
        echo "✓ Sample servers completed!<br>";
    } else {
        echo "✓ Servers table already has data (" . $row['count'] . " servers)<br>";
    }
}

// Check custom_ads table
$result = mysqli_query($conn, "SELECT COUNT(*) as count FROM custom_ads");
if ($result) {
    $row = mysqli_fetch_assoc($result);
    if ($row['count'] == 0) {
        echo "Adding sample ads...<br>";
        $sample_ads = [
            "INSERT INTO custom_ads (title, description, date_start, date_end, `on`, view_count, click_count) VALUES ('Welcome Ad', 'Welcome to 5G Smart VPN!', CURDATE(), DATE_ADD(CURDATE(), INTERVAL 30 DAY), 1, 150, 12)",
            "INSERT INTO custom_ads (title, description, date_start, date_end, `on`, view_count, click_count) VALUES ('Premium Upgrade', 'Upgrade to Premium for faster speeds!', CURDATE(), DATE_ADD(CURDATE(), INTERVAL 60 DAY), 1, 89, 7)"
        ];

        foreach ($sample_ads as $sql) {
            if (mysqli_query($conn, $sql)) {
                echo "✓ Ad added<br>";
            } else {
                echo "✗ Error adding ad: " . mysqli_error($conn) . "<br>";
            }
        }
        echo "✓ Sample ads completed!<br>";
    } else {
        echo "✓ Custom ads table already has data (" . $row['count'] . " ads)<br>";
    }
}

// Check notifications table
$result = mysqli_query($conn, "SELECT COUNT(*) as count FROM notifications");
if ($result) {
    $row = mysqli_fetch_assoc($result);
    if ($row['count'] == 0) {
        echo "Adding sample notifications...<br>";
        $sample_notifications = [
            "INSERT INTO notifications (title, message, status) VALUES ('Welcome', 'Welcome to 5G Smart VPN Admin Panel!', 'sent')",
            "INSERT INTO notifications (title, message, status) VALUES ('System Update', 'System maintenance scheduled for tonight.', 'pending')"
        ];

        foreach ($sample_notifications as $sql) {
            if (mysqli_query($conn, $sql)) {
                echo "✓ Notification added<br>";
            } else {
                echo "✗ Error adding notification: " . mysqli_error($conn) . "<br>";
            }
        }
        echo "✓ Sample notifications completed!<br>";
    } else {
        echo "✓ Notifications table already has data (" . $row['count'] . " notifications)<br>";
    }
}

// Check contact table
$result = mysqli_query($conn, "SELECT COUNT(*) as count FROM contact");
if ($result) {
    $row = mysqli_fetch_assoc($result);
    if ($row['count'] == 0) {
        echo "Adding sample contacts...<br>";
        $sample_contacts = [
            "INSERT INTO contact (name, email, subject, message, unread) VALUES ('John Doe', '<EMAIL>', 'Connection Issue', 'Having trouble connecting to servers.', 1)",
            "INSERT INTO contact (name, email, subject, message, unread) VALUES ('Jane Smith', '<EMAIL>', 'Feature Request', 'Can you add more server locations?', 0)"
        ];

        foreach ($sample_contacts as $sql) {
            if (mysqli_query($conn, $sql)) {
                echo "✓ Contact added<br>";
            } else {
                echo "✗ Error adding contact: " . mysqli_error($conn) . "<br>";
            }
        }
        echo "✓ Sample contacts completed!<br>";
    } else {
        echo "✓ Contact table already has data (" . $row['count'] . " contacts)<br>";
    }
}

echo "<br><h2>Database setup complete!</h2>";
echo "You can now try accessing the admin panel again.";

mysqli_close($conn);
?>
