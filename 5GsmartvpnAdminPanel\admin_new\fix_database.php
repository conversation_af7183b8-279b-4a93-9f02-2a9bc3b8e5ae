<?php
/**
 * Comprehensive Database Setup and Fix Script
 * This script will check and create all missing database tables
 * Version: 3.0 - Complete Setup
 */

error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>🔧 Comprehensive Database Setup Script</h1>";
echo "<style>
    body { font-family: Arial, sans-serif; margin: 20px; }
    .success { color: #28a745; }
    .error { color: #dc3545; }
    .warning { color: #ffc107; }
    .info { color: #17a2b8; }
    h2 { border-bottom: 2px solid #007bff; padding-bottom: 5px; }
</style>";

// Database connection using unified config
require_once 'includes/config.php';

if (!$conn) {
    die("<p class='error'>❌ Connection failed: " . mysqli_connect_error() . "</p>");
}

echo "<p class='success'>✅ Connected to database successfully!</p><br>";

// Check and create tables
$tables_to_check = [
    'servers' => "
        CREATE TABLE IF NOT EXISTS `servers` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `name` varchar(255) NOT NULL,
            `flagURL` varchar(255) DEFAULT NULL,
            `type` varchar(50) DEFAULT 'free',
            `status` tinyint(1) DEFAULT 1,
            `pos` int(11) DEFAULT 0,
            `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
    ",
    
    'custom_ads' => "
        CREATE TABLE IF NOT EXISTS `custom_ads` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `title` varchar(255) NOT NULL,
            `description` text,
            `image_url` varchar(255) DEFAULT NULL,
            `click_url` varchar(255) DEFAULT NULL,
            `date_start` date DEFAULT NULL,
            `date_end` date DEFAULT NULL,
            `on` tinyint(1) DEFAULT 1,
            `view_count` int(11) DEFAULT 0,
            `click_count` int(11) DEFAULT 0,
            `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
    ",
    
    'notifications' => "
        CREATE TABLE IF NOT EXISTS `notifications` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `title` varchar(255) NOT NULL,
            `message` text NOT NULL,
            `status` enum('pending','sent','failed','scheduled') DEFAULT 'pending',
            `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
    ",
    
    'contact' => "
        CREATE TABLE IF NOT EXISTS `contact` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `name` varchar(255) NOT NULL,
            `email` varchar(255) NOT NULL,
            `subject` varchar(255) DEFAULT NULL,
            `message` text NOT NULL,
            `unread` tinyint(1) DEFAULT 1,
            `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
    ",
    
    'admin_logs' => "
        CREATE TABLE IF NOT EXISTS `admin_logs` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `admin_id` int(11) DEFAULT 1,
            `admin_username` varchar(255) DEFAULT 'admin',
            `action` varchar(100) DEFAULT NULL,
            `description` text,
            `ip_address` varchar(45) DEFAULT '127.0.0.1',
            `user_agent` text,
            `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`),
            KEY `idx_created_at` (`created_at`),
            KEY `idx_action` (`action`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
    ",

    'admin_users' => "
        CREATE TABLE IF NOT EXISTS `admin_users` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `username` varchar(255) NOT NULL UNIQUE,
            `password` varchar(255) NOT NULL,
            `email` varchar(255) DEFAULT NULL,
            `role` enum('admin','moderator','viewer') DEFAULT 'admin',
            `status` tinyint(1) DEFAULT 1,
            `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`),
            UNIQUE KEY `username` (`username`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
    ",

    'settings' => "
        CREATE TABLE IF NOT EXISTS `settings` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `setting_key` varchar(255) NOT NULL UNIQUE,
            `setting_value` text,
            `description` text,
            `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
            `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`),
            UNIQUE KEY `setting_key` (`setting_key`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
    "
];

foreach ($tables_to_check as $table_name => $create_sql) {
    // Check if table exists
    $result = mysqli_query($conn, "SHOW TABLES LIKE '$table_name'");
    
    if (mysqli_num_rows($result) == 0) {
        echo "Table '$table_name' does not exist. Creating...<br>";
        
        if (mysqli_query($conn, $create_sql)) {
            echo "✓ Table '$table_name' created successfully!<br>";
        } else {
            echo "✗ Error creating table '$table_name': " . mysqli_error($conn) . "<br>";
        }
    } else {
        echo "✓ Table '$table_name' already exists.<br>";
    }
}

// Insert sample data if tables are empty
echo "<br><h2>Checking for sample data...</h2>";

// Check servers table
$result = mysqli_query($conn, "SELECT COUNT(*) as count FROM servers");
if ($result) {
    $row = mysqli_fetch_assoc($result);
    if ($row['count'] == 0) {
        echo "Adding sample servers...<br>";
        $sample_servers = [
            "INSERT INTO servers (name, flagURL, type, status, pos) VALUES ('USA Server', 'flag/united-states-of-america.png', 'free', 1, 1)",
            "INSERT INTO servers (name, flagURL, type, status, pos) VALUES ('UK Server', 'flag/united-kingdom.png', 'premium', 1, 2)",
            "INSERT INTO servers (name, flagURL, type, status, pos) VALUES ('Germany Server', 'flag/germany.png', 'free', 1, 3)",
            "INSERT INTO servers (name, flagURL, type, status, pos) VALUES ('Canada Server', 'flag/canada.png', 'free', 1, 4)",
            "INSERT INTO servers (name, flagURL, type, status, pos) VALUES ('Japan Server', 'flag/japan.png', 'premium', 1, 5)"
        ];

        foreach ($sample_servers as $sql) {
            if (mysqli_query($conn, $sql)) {
                echo "✓ Server added<br>";
            } else {
                echo "✗ Error adding server: " . mysqli_error($conn) . "<br>";
            }
        }
        echo "✓ Sample servers completed!<br>";
    } else {
        echo "✓ Servers table already has data (" . $row['count'] . " servers)<br>";
    }
}

// Check custom_ads table
$result = mysqli_query($conn, "SELECT COUNT(*) as count FROM custom_ads");
if ($result) {
    $row = mysqli_fetch_assoc($result);
    if ($row['count'] == 0) {
        echo "Adding sample ads...<br>";
        $sample_ads = [
            "INSERT INTO custom_ads (title, description, date_start, date_end, `on`, view_count, click_count) VALUES ('Welcome Ad', 'Welcome to 5G Smart VPN!', CURDATE(), DATE_ADD(CURDATE(), INTERVAL 30 DAY), 1, 150, 12)",
            "INSERT INTO custom_ads (title, description, date_start, date_end, `on`, view_count, click_count) VALUES ('Premium Upgrade', 'Upgrade to Premium for faster speeds!', CURDATE(), DATE_ADD(CURDATE(), INTERVAL 60 DAY), 1, 89, 7)"
        ];

        foreach ($sample_ads as $sql) {
            if (mysqli_query($conn, $sql)) {
                echo "✓ Ad added<br>";
            } else {
                echo "✗ Error adding ad: " . mysqli_error($conn) . "<br>";
            }
        }
        echo "✓ Sample ads completed!<br>";
    } else {
        echo "✓ Custom ads table already has data (" . $row['count'] . " ads)<br>";
    }
}

// Check notifications table
$result = mysqli_query($conn, "SELECT COUNT(*) as count FROM notifications");
if ($result) {
    $row = mysqli_fetch_assoc($result);
    if ($row['count'] == 0) {
        echo "Adding sample notifications...<br>";
        $sample_notifications = [
            "INSERT INTO notifications (title, message, status) VALUES ('Welcome', 'Welcome to 5G Smart VPN Admin Panel!', 'sent')",
            "INSERT INTO notifications (title, message, status) VALUES ('System Update', 'System maintenance scheduled for tonight.', 'pending')"
        ];

        foreach ($sample_notifications as $sql) {
            if (mysqli_query($conn, $sql)) {
                echo "✓ Notification added<br>";
            } else {
                echo "✗ Error adding notification: " . mysqli_error($conn) . "<br>";
            }
        }
        echo "✓ Sample notifications completed!<br>";
    } else {
        echo "✓ Notifications table already has data (" . $row['count'] . " notifications)<br>";
    }
}

// Check contact table
$result = mysqli_query($conn, "SELECT COUNT(*) as count FROM contact");
if ($result) {
    $row = mysqli_fetch_assoc($result);
    if ($row['count'] == 0) {
        echo "Adding sample contacts...<br>";
        $sample_contacts = [
            "INSERT INTO contact (name, email, subject, message, unread) VALUES ('John Doe', '<EMAIL>', 'Connection Issue', 'Having trouble connecting to servers.', 1)",
            "INSERT INTO contact (name, email, subject, message, unread) VALUES ('Jane Smith', '<EMAIL>', 'Feature Request', 'Can you add more server locations?', 0)"
        ];

        foreach ($sample_contacts as $sql) {
            if (mysqli_query($conn, $sql)) {
                echo "✓ Contact added<br>";
            } else {
                echo "✗ Error adding contact: " . mysqli_error($conn) . "<br>";
            }
        }
        echo "✓ Sample contacts completed!<br>";
    } else {
        echo "✓ Contact table already has data (" . $row['count'] . " contacts)<br>";
    }
}

// Create default admin user if admin_users table is empty
$result = mysqli_query($conn, "SELECT COUNT(*) as count FROM admin_users");
if ($result) {
    $row = mysqli_fetch_assoc($result);
    if ($row['count'] == 0) {
        echo "Adding default admin user...<br>";
        $admin_sql = "INSERT INTO admin_users (username, password, email, role) VALUES ('admin', '1234', '<EMAIL>', 'admin')";
        if (mysqli_query($conn, $admin_sql)) {
            echo "<p class='success'>✓ Default admin user created (username: admin, password: 1234)</p>";
        } else {
            echo "<p class='error'>✗ Error creating admin user: " . mysqli_error($conn) . "</p>";
        }
    } else {
        echo "<p class='info'>✓ Admin users table already has data (" . $row['count'] . " users)</p>";
    }
}

// Initialize default settings
$default_settings = [
    'app_name' => '5G Smart VPN',
    'app_version' => '3.0.0',
    'admob_app_id' => '',
    'admob_banner_id' => '',
    'admob_interstitial_id' => '',
    'admob_native_id' => '',
    'admob_rewarded_id' => '',
    'admob_openad_id' => '',
    'banner_enabled' => '1',
    'interstitial_enabled' => '1',
    'rewarded_enabled' => '0',
    'native_enabled' => '0',
    'openad_enabled' => '1',
    'test_mode' => '0',
    'ads_status' => '1'
];

echo "<h2>Initializing Default Settings</h2>";
foreach ($default_settings as $key => $value) {
    $check_sql = "SELECT COUNT(*) as count FROM settings WHERE setting_key = '$key'";
    $result = mysqli_query($conn, $check_sql);
    if ($result) {
        $row = mysqli_fetch_assoc($result);
        if ($row['count'] == 0) {
            $insert_sql = "INSERT INTO settings (setting_key, setting_value) VALUES ('$key', '$value')";
            if (mysqli_query($conn, $insert_sql)) {
                echo "<p class='success'>✓ Setting '$key' initialized</p>";
            } else {
                echo "<p class='error'>✗ Error setting '$key': " . mysqli_error($conn) . "</p>";
            }
        }
    }
}

echo "<br><h2>🎉 Database setup complete!</h2>";
echo "<p class='success'>All required tables have been created and initialized.</p>";
echo "<p><strong>Next Steps:</strong></p>";
echo "<ul>";
echo "<li><a href='login.php'>Login to Admin Panel</a> (username: admin, password: 1234)</li>";
echo "<li><a href='index.php'>Access Dashboard</a></li>";
echo "<li><a href='api/status.php'>Test API Endpoints</a></li>";
echo "</ul>";

mysqli_close($conn);
?>
