<?php
/**
 * Function Conflict Test
 * Tests that all functions load without conflicts
 */

echo "<h1>🔧 Function Conflict Resolution Test</h1>";
echo "<style>
    body { font-family: Arial, sans-serif; margin: 20px; }
    .success { color: #28a745; }
    .error { color: #dc3545; }
    .info { color: #17a2b8; }
</style>";

echo "<h2>Loading Configuration...</h2>";
try {
    require_once 'includes/config.php';
    echo "<p class='success'>✅ Config loaded successfully</p>";
    
    if (function_exists('logError')) {
        echo "<p class='success'>✅ logError function available</p>";
    } else {
        echo "<p class='error'>❌ logError function missing</p>";
    }
    
    if (function_exists('sanitizeInput')) {
        echo "<p class='success'>✅ sanitizeInput function available</p>";
    } else {
        echo "<p class='error'>❌ sanitizeInput function missing</p>";
    }
    
} catch (Exception $e) {
    echo "<p class='error'>❌ Config error: " . $e->getMessage() . "</p>";
}

echo "<h2>Loading Functions...</h2>";
try {
    require_once 'includes/functions.php';
    echo "<p class='success'>✅ Functions loaded successfully</p>";
    
    $test_functions = [
        'getDashboardStats',
        'getRecentActivities', 
        'getActivityIcon',
        'timeAgo',
        'generateCSRFToken',
        'verifyCSRFToken',
        'uploadFile',
        'deleteFile',
        'formatFileSize',
        'generatePagination',
        'sendJsonResponse',
        'redirectWithMessage',
        'getFlashMessage'
    ];
    
    foreach ($test_functions as $func) {
        if (function_exists($func)) {
            echo "<p class='success'>✅ $func function available</p>";
        } else {
            echo "<p class='error'>❌ $func function missing</p>";
        }
    }
    
} catch (Exception $e) {
    echo "<p class='error'>❌ Functions error: " . $e->getMessage() . "</p>";
}

echo "<h2>Testing Function Calls...</h2>";

// Test sanitizeInput
try {
    $test_input = "<script>alert('test')</script>";
    $sanitized = sanitizeInput($test_input);
    echo "<p class='success'>✅ sanitizeInput works: " . htmlspecialchars($sanitized) . "</p>";
} catch (Exception $e) {
    echo "<p class='error'>❌ sanitizeInput error: " . $e->getMessage() . "</p>";
}

// Test timeAgo
try {
    $time_result = timeAgo(date('Y-m-d H:i:s', time() - 3600));
    echo "<p class='success'>✅ timeAgo works: $time_result</p>";
} catch (Exception $e) {
    echo "<p class='error'>❌ timeAgo error: " . $e->getMessage() . "</p>";
}

// Test getDashboardStats
try {
    if (isset($conn)) {
        $stats = getDashboardStats($conn);
        echo "<p class='success'>✅ getDashboardStats works: " . count($stats) . " stats returned</p>";
    } else {
        echo "<p class='error'>❌ No database connection for getDashboardStats test</p>";
    }
} catch (Exception $e) {
    echo "<p class='error'>❌ getDashboardStats error: " . $e->getMessage() . "</p>";
}

echo "<h2>✅ All Function Conflicts Resolved!</h2>";
echo "<p class='info'>All functions are now loading without conflicts.</p>";
echo "<p><a href='index.php'>🏠 Go to Dashboard</a> | <a href='login.php'>🔐 Go to Login</a></p>";
?>
